# Chrome MCP 远程部署指南

本指南说明如何在一台机器上部署Chrome MCP服务器，并从其他工作区远程调用。

## 架构概述

```
工作区A (************)          工作区B (其他机器)
┌─────────────────────┐         ┌─────────────────────┐
│  Chrome MCP Server  │◄────────│   MCP Client/Proxy  │
│  HTTP API (3001)    │         │                     │
│  Chrome Browser     │         │   Claude Desktop    │
│  MES System (3000)  │         │   或其他MCP客户端    │
└─────────────────────┘         └─────────────────────┘
```

## 服务器端部署 (************)

### 1. 环境准备
```bash
# 确保Node.js已安装
node --version
npm --version

# 确保Chrome已安装
# Windows: 检查 C:\Program Files\Google\Chrome\Application\chrome.exe
```

### 2. 启动MCP服务器
```bash
# 在chrome-mcp目录下
cd C:\Users\<USER>\chrome-mcp

# 安装依赖
npm install

# 启动HTTP MCP服务器
npm run server
```

服务器将在 http://************:3001 启动

### 3. 验证服务器
```bash
# 检查健康状态
curl http://************:3001/health

# 查看可用工具
curl http://************:3001/tools
```

### 4. 防火墙配置
确保端口3001对内网开放：
```powershell
# Windows防火墙规则
New-NetFirewallRule -DisplayName "MCP Server" -Direction Inbound -Protocol TCP -LocalPort 3001 -Action Allow
```

## 客户端配置

### 方法一：HTTP API直接调用

从任何支持HTTP的环境调用：

```bash
# 连接到Chrome
curl -X POST http://************:3001/connect

# 导航到页面
curl -X POST http://************:3001/navigate \
  -H "Content-Type: application/json" \
  -d '{"url": "http://************:3000"}'

# 截图
curl -X POST http://************:3001/screenshot \
  -H "Content-Type: application/json" \
  -d '{"filename": "remote-screenshot.png"}'

# 执行JavaScript
curl -X POST http://************:3001/evaluate \
  -H "Content-Type: application/json" \
  -d '{"code": "document.title"}'

# 测试MES系统
curl -X POST http://************:3001/test-mes
```

### 方法二：MCP代理配置

在客户端工作区配置MCP代理：

1. **复制代理文件**
   ```bash
   # 将remote-mcp-proxy.js复制到客户端机器
   scp remote-mcp-proxy.js user@client-machine:/path/to/mcp/
   ```

2. **配置MCP客户端**
   
   在Claude Desktop或其他MCP客户端的配置文件中添加：
   ```json
   {
     "mcpServers": {
       "remote-chrome": {
         "command": "node",
         "args": ["/path/to/remote-mcp-proxy.js"],
         "env": {
           "REMOTE_MCP_SERVER": "http://************:3001"
         }
       }
     }
   }
   ```

3. **重启MCP客户端**

### 方法三：JavaScript客户端

使用提供的JavaScript客户端：

```javascript
const MCPClient = require('./mcp-client-example.js');

const client = new MCPClient('http://************:3001');

async function remoteTest() {
    // 检查连接
    await client.checkHealth();
    
    // 连接Chrome
    await client.connect();
    
    // 导航和测试
    await client.navigate('http://************:3000');
    await client.screenshot('remote-test.png');
    await client.testMES();
}

remoteTest();
```

## 可用的API端点

| 端点 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/health` | GET | 健康检查 | 无 |
| `/tools` | GET | 获取工具列表 | 无 |
| `/connect` | POST | 连接Chrome | 无 |
| `/navigate` | POST | 导航到URL | `{"url": "..."}` |
| `/screenshot` | POST | 截图 | `{"filename": "..."}` |
| `/click` | POST | 点击元素 | `{"selector": "..."}` |
| `/type` | POST | 输入文本 | `{"selector": "...", "text": "..."}` |
| `/evaluate` | POST | 执行JS | `{"code": "..."}` |
| `/page-info` | GET | 页面信息 | 无 |
| `/test-mes` | POST | MES测试 | 无 |

## 故障排除

### 服务器端问题

1. **端口被占用**
   ```bash
   # 检查端口使用
   netstat -ano | findstr :3001
   
   # 修改端口
   set MCP_PORT=3002
   npm run server
   ```

2. **Chrome启动失败**
   ```bash
   # 手动启动Chrome调试模式
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --disable-web-security
   ```

3. **MES系统无法访问**
   ```bash
   # 检查MES服务器
   curl http://************:3000
   
   # 修改MES URL
   set MES_SERVER_URL=http://other-server:3000
   ```

### 客户端问题

1. **网络连接**
   ```bash
   # 测试网络连通性
   ping ************
   telnet ************ 3001
   ```

2. **代理配置**
   ```bash
   # 测试代理
   node remote-mcp-proxy.js
   # 然后发送测试消息
   ```

## 安全考虑

1. **网络安全**
   - 仅在受信任的内网环境中使用
   - 考虑使用VPN或SSH隧道
   - 限制防火墙规则到特定IP

2. **访问控制**
   - 添加API密钥认证
   - 实现请求频率限制
   - 记录访问日志

3. **数据安全**
   - 截图文件的存储和清理
   - 敏感信息的处理
   - 日志文件的管理

## 扩展功能

1. **负载均衡**
   - 多个Chrome实例
   - 请求分发

2. **监控和日志**
   - 性能监控
   - 错误追踪
   - 使用统计

3. **高可用性**
   - 自动重启
   - 健康检查
   - 故障转移
