{"name": "chrome-mcp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node start-mcp-server.js", "server": "node mcp-http-server.js", "client": "node mcp-client-example.js", "proxy": "node remote-mcp-proxy.js", "test": "node test-chrome-mcp.js", "chrome": "powershell -ExecutionPolicy Bypass -File start-chrome-debug.ps1", "connect": "node chrome-mcp-server.js connect", "mes-test": "node chrome-mcp-server.js test", "health": "node mcp-client-example.js health", "tools": "node mcp-client-example.js tools"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"chrome-launcher": "^1.2.0", "cors": "^2.8.5", "express": "^5.1.0", "playwright": "^1.53.1", "puppeteer": "^24.10.2", "ws": "^8.18.2"}}