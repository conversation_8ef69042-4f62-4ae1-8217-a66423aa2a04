#!/usr/bin/env node

// 远程MCP代理 - 将MCP调用转发到远程HTTP服务器
const http = require('http');

class RemoteMCPProxy {
    constructor() {
        this.serverUrl = process.env.REMOTE_MCP_SERVER || 'http://************:3001';
        this.tools = [
            {
                name: "navigate",
                description: "Navigate to a URL",
                inputSchema: {
                    type: "object",
                    properties: {
                        url: { type: "string", description: "URL to navigate to" }
                    },
                    required: ["url"]
                }
            },
            {
                name: "screenshot",
                description: "Take a screenshot",
                inputSchema: {
                    type: "object",
                    properties: {
                        filename: { type: "string", description: "Screenshot filename" }
                    }
                }
            },
            {
                name: "click",
                description: "Click an element",
                inputSchema: {
                    type: "object",
                    properties: {
                        selector: { type: "string", description: "CSS selector" }
                    },
                    required: ["selector"]
                }
            },
            {
                name: "type",
                description: "Type text into an element",
                inputSchema: {
                    type: "object",
                    properties: {
                        selector: { type: "string", description: "CSS selector" },
                        text: { type: "string", description: "Text to type" }
                    },
                    required: ["selector", "text"]
                }
            },
            {
                name: "evaluate",
                description: "Execute JavaScript code",
                inputSchema: {
                    type: "object",
                    properties: {
                        code: { type: "string", description: "JavaScript code" }
                    },
                    required: ["code"]
                }
            },
            {
                name: "get_page_info",
                description: "Get page information",
                inputSchema: {
                    type: "object",
                    properties: {}
                }
            },
            {
                name: "test_mes_system",
                description: "Test MES system",
                inputSchema: {
                    type: "object",
                    properties: {}
                }
            }
        ];
    }

    // HTTP请求函数
    request(method, path, data = null) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, this.serverUrl);
            const options = {
                hostname: url.hostname,
                port: url.port,
                path: url.pathname,
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            const req = http.request(options, (res) => {
                let responseData = '';
                res.on('data', (chunk) => responseData += chunk);
                res.on('end', () => {
                    try {
                        resolve(JSON.parse(responseData));
                    } catch (error) {
                        reject(new Error(`解析响应失败: ${error.message}`));
                    }
                });
            });

            req.on('error', reject);

            if (data) {
                req.write(JSON.stringify(data));
            }
            req.end();
        });
    }

    // 执行远程工具调用
    async executeRemoteTool(name, args) {
        let apiPath, apiData, method = 'POST';

        switch (name) {
            case 'navigate':
                apiPath = '/navigate';
                apiData = { url: args.url };
                break;
            case 'screenshot':
                apiPath = '/screenshot';
                apiData = { filename: args.filename };
                break;
            case 'click':
                apiPath = '/click';
                apiData = { selector: args.selector };
                break;
            case 'type':
                apiPath = '/type';
                apiData = { selector: args.selector, text: args.text };
                break;
            case 'evaluate':
                apiPath = '/evaluate';
                apiData = { code: args.code };
                break;
            case 'get_page_info':
                apiPath = '/page-info';
                method = 'GET';
                apiData = null;
                break;
            case 'test_mes_system':
                apiPath = '/test-mes';
                apiData = null;
                break;
            default:
                throw new Error(`未知工具: ${name}`);
        }

        return await this.request(method, apiPath, apiData);
    }

    // 处理MCP消息
    async handleMessage(message) {
        try {
            switch (message.method) {
                case 'initialize':
                    return {
                        jsonrpc: "2.0",
                        id: message.id,
                        result: {
                            protocolVersion: "2024-11-05",
                            capabilities: {
                                tools: {}
                            },
                            serverInfo: {
                                name: "remote-chrome-mcp-proxy",
                                version: "1.0.0"
                            }
                        }
                    };

                case 'tools/list':
                    return {
                        jsonrpc: "2.0",
                        id: message.id,
                        result: {
                            tools: this.tools
                        }
                    };

                case 'tools/call':
                    const { name, arguments: args } = message.params;
                    const result = await this.executeRemoteTool(name, args || {});
                    return {
                        jsonrpc: "2.0",
                        id: message.id,
                        result: {
                            content: [
                                {
                                    type: "text",
                                    text: JSON.stringify(result, null, 2)
                                }
                            ]
                        }
                    };

                default:
                    throw new Error(`未知方法: ${message.method}`);
            }
        } catch (error) {
            return {
                jsonrpc: "2.0",
                id: message.id,
                error: {
                    code: -32603,
                    message: error.message
                }
            };
        }
    }

    // 启动代理
    start() {
        console.error(`远程MCP代理启动，连接到: ${this.serverUrl}`);
        
        // 处理stdin/stdout通信
        process.stdin.on('data', async (data) => {
            const lines = data.toString().trim().split('\n');
            
            for (const line of lines) {
                if (line.trim()) {
                    try {
                        const message = JSON.parse(line);
                        const response = await this.handleMessage(message);
                        console.log(JSON.stringify(response));
                    } catch (error) {
                        console.error('消息处理错误:', error.message);
                    }
                }
            }
        });

        process.stdin.resume();
        console.error('代理已启动，等待MCP消息...');
    }
}

// 启动代理
if (require.main === module) {
    const proxy = new RemoteMCPProxy();
    proxy.start();
}

module.exports = RemoteMCPProxy;
