# Chrome MCP 远程调试配置

这个项目提供了一个完整的Chrome MCP (Model Context Protocol) 配置，支持本地和远程调试web应用。

## 功能特性

- 🚀 自动启动Chrome调试模式
- 🔧 连接到远程MES系统 (http://************:3000)
- 📸 自动截图和页面测试
- 💻 交互式命令行界面
- 🔍 页面元素检查和JavaScript执行
- 🌐 HTTP API服务器，支持远程调用
- 📡 标准MCP协议支持
- 🔗 跨工作区调用支持

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动HTTP MCP服务器（推荐用于远程调用）
```bash
# 启动HTTP API服务器
npm run server
```

服务器将在 http://localhost:3001 启动

### 3. 从其他工作区调用MCP服务

#### 方法一：使用HTTP API
```bash
# 检查服务器状态
curl http://localhost:3001/health

# 连接到Chrome
curl -X POST http://localhost:3001/connect

# 导航到页面
curl -X POST http://localhost:3001/navigate \
  -H "Content-Type: application/json" \
  -d '{"url": "http://************:3000"}'

# 截图
curl -X POST http://localhost:3001/screenshot \
  -H "Content-Type: application/json" \
  -d '{"filename": "test.png"}'
```

#### 方法二：使用客户端示例
```bash
# 运行完整示例
npm run client

# 或者单独执行命令
node mcp-client-example.js health
node mcp-client-example.js navigate http://************:3000
node mcp-client-example.js screenshot
node mcp-client-example.js test
```

### 4. 本地交互式模式
```bash
# 启动交互式MCP服务器
npm start

# 或者直接运行测试
npm run test
```

## 使用方法

### 自动模式
```bash
# 运行完整的MES系统测试
npm run mes-test

# 运行基础连接测试
npm run test
```

### 交互式模式
启动交互式服务器后，可以使用以下命令：

```
MCP> info          # 获取当前页面信息
MCP> test          # 运行MES系统测试
MCP> nav <url>     # 导航到指定URL
MCP> shot [file]   # 截图保存
MCP> eval <code>   # 执行JavaScript代码
MCP> quit          # 退出
```

### 示例用法
```bash
# 导航到特定页面
MCP> nav http://************:3000/dashboard

# 截图
MCP> shot dashboard.png

# 执行JavaScript
MCP> eval document.title

# 检查页面元素
MCP> eval document.querySelector('.ant-menu') !== null
```

## 配置说明

### mcp-config.json
MCP服务器的配置文件，包含：
- Chrome WebSocket端点: `ws://localhost:9222`
- 目标服务器URL: `http://************:3000`
- 环境变量配置

### Chrome启动参数
- `--remote-debugging-port=9222`: 启用远程调试
- `--disable-web-security`: 禁用跨域限制
- `--user-data-dir`: 使用临时用户数据目录

## 故障排除

### Chrome无法启动
1. 确保Chrome已安装
2. 检查端口9222是否被占用
3. 手动关闭所有Chrome进程后重试

### 无法连接到MES系统
1. 确认MES服务器运行在 http://************:3000
2. 检查网络连接
3. 修改 `mcp-config.json` 中的 `MES_SERVER_URL`

### 权限问题
在PowerShell中运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 文件结构

```
chrome-mcp/
├── chrome-mcp-server.js      # 主要的MCP服务器
├── start-mcp-server.js       # 服务器启动管理器
├── test-chrome-mcp.js        # 测试脚本
├── mcp-config.json           # MCP配置文件
├── start-chrome-debug.bat    # Windows批处理启动脚本
├── start-chrome-debug.ps1    # PowerShell启动脚本
├── package.json              # Node.js项目配置
└── README.md                 # 说明文档
```

## 高级用法

### 自定义测试
修改 `chrome-mcp-server.js` 中的 `testMESSystem()` 方法来添加自定义测试逻辑。

### 修改目标URL
在 `mcp-config.json` 中修改 `MES_SERVER_URL` 环境变量。

### 添加新命令
在 `start-mcp-server.js` 的交互式模式中添加新的命令处理逻辑。

## 技术栈

- **Node.js**: 运行时环境
- **Puppeteer**: Chrome自动化
- **WebSocket**: 实时通信
- **Chrome DevTools Protocol**: 浏览器调试接口
