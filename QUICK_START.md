# Chrome MCP 快速开始指南

## 🎯 目标
在一台机器上运行Chrome MCP服务器，从其他工作区远程调用进行web应用调试。

## 🚀 快速部署

### 服务器端 (************)

1. **启动MCP服务器**
   ```bash
   cd C:\Users\<USER>\chrome-mcp
   npm run server
   ```
   
   服务器将在 http://************:3001 启动

2. **验证服务**
   ```bash
   # 健康检查
   curl http://************:3001/health
   
   # 查看工具
   curl http://************:3001/tools
   ```

### 客户端调用

#### 方法1: HTTP API (推荐)
```bash
# 连接Chrome
curl -X POST http://************:3001/connect

# 导航页面
curl -X POST http://************:3001/navigate \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'

# 截图
curl -X POST http://************:3001/screenshot \
  -H "Content-Type: application/json" \
  -d '{"filename": "test.png"}'

# 执行JavaScript
curl -X POST http://************:3001/evaluate \
  -H "Content-Type: application/json" \
  -d '{"code": "document.title"}'
```

#### 方法2: JavaScript客户端
```javascript
const MCPClient = require('./mcp-client-example.js');

const client = new MCPClient('http://************:3001');

async function test() {
    await client.connect();
    await client.navigate('https://example.com');
    await client.screenshot('remote.png');
}

test();
```

#### 方法3: MCP代理 (用于Claude Desktop等)
1. 复制 `remote-mcp-proxy.js` 到客户端机器
2. 在MCP配置中添加：
   ```json
   {
     "mcpServers": {
       "remote-chrome": {
         "command": "node",
         "args": ["remote-mcp-proxy.js"],
         "env": {
           "REMOTE_MCP_SERVER": "http://************:3001"
         }
       }
     }
   }
   ```

## 📋 可用功能

| 功能 | API端点 | 描述 |
|------|---------|------|
| 连接Chrome | `POST /connect` | 启动并连接Chrome浏览器 |
| 导航 | `POST /navigate` | 导航到指定URL |
| 截图 | `POST /screenshot` | 对当前页面截图 |
| 点击 | `POST /click` | 点击页面元素 |
| 输入 | `POST /type` | 在元素中输入文本 |
| 执行JS | `POST /evaluate` | 执行JavaScript代码 |
| 页面信息 | `GET /page-info` | 获取页面标题、URL等信息 |
| MES测试 | `POST /test-mes` | 运行MES系统自动化测试 |

## 🔧 配置说明

### 环境变量
- `MCP_PORT`: HTTP服务器端口 (默认: 3001)
- `MES_SERVER_URL`: MES系统URL (默认: http://************:3000)
- `REMOTE_MCP_SERVER`: 远程MCP服务器地址 (客户端用)

### 网络要求
- 服务器端口3001需要对内网开放
- Chrome调试端口9222 (自动管理)
- 目标应用端口 (如MES系统的3000端口)

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查服务器状态
   curl http://************:3001/health
   
   # 检查网络连通性
   ping ************
   telnet ************ 3001
   ```

2. **Chrome启动失败**
   ```bash
   # 手动启动Chrome调试模式
   "C:\Program Files\Google\Chrome\Application\chrome.exe" \
     --remote-debugging-port=9222 --disable-web-security
   ```

3. **权限问题**
   ```powershell
   # Windows防火墙
   New-NetFirewallRule -DisplayName "MCP Server" -Direction Inbound -Protocol TCP -LocalPort 3001 -Action Allow
   ```

### 日志查看
服务器日志会显示所有请求和Chrome操作状态。

## 📁 文件结构

```
chrome-mcp/
├── mcp-http-server.js        # HTTP MCP服务器 (主要)
├── remote-mcp-proxy.js       # 远程MCP代理
├── mcp-client-example.js     # JavaScript客户端示例
├── remote-config-simple.json # 远程配置示例
├── DEPLOYMENT.md             # 详细部署指南
└── README.md                 # 完整文档
```

## 🎉 成功验证

如果看到以下输出，说明配置成功：

1. **服务器启动**
   ```
   HTTP MCP服务器启动在端口 3001
   访问 http://localhost:3001/health 检查状态
   ```

2. **健康检查**
   ```json
   {"status":"ok","browser":"connected","timestamp":"..."}
   ```

3. **工具调用成功**
   ```json
   {"success":true,"filename":"test.png"}
   ```

现在你可以从任何工作区远程调用Chrome MCP服务来调试web应用了！
