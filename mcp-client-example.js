#!/usr/bin/env node

// MCP客户端示例 - 展示如何从其他工作区调用MCP服务
const http = require('http');

class MCPClient {
    constructor(serverUrl = 'http://localhost:3001') {
        this.serverUrl = serverUrl;
    }

    // HTTP请求辅助函数
    async request(method, path, data = null) {
        return new Promise((resolve, reject) => {
            const url = new URL(path, this.serverUrl);
            const options = {
                hostname: url.hostname,
                port: url.port,
                path: url.pathname,
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            const req = http.request(options, (res) => {
                let responseData = '';
                res.on('data', (chunk) => responseData += chunk);
                res.on('end', () => {
                    try {
                        const result = JSON.parse(responseData);
                        resolve(result);
                    } catch (error) {
                        reject(new Error(`解析响应失败: ${error.message}`));
                    }
                });
            });

            req.on('error', reject);

            if (data) {
                req.write(JSON.stringify(data));
            }
            req.end();
        });
    }

    // 检查服务器状态
    async checkHealth() {
        try {
            const result = await this.request('GET', '/health');
            console.log('服务器状态:', result);
            return result.status === 'ok';
        } catch (error) {
            console.error('健康检查失败:', error.message);
            return false;
        }
    }

    // 获取可用工具
    async getTools() {
        try {
            const result = await this.request('GET', '/tools');
            console.log('可用工具:');
            result.tools.forEach(tool => {
                console.log(`  - ${tool.name}: ${tool.description}`);
                if (tool.params) {
                    console.log(`    参数: ${tool.params.join(', ')}`);
                }
            });
            return result.tools;
        } catch (error) {
            console.error('获取工具列表失败:', error.message);
            return [];
        }
    }

    // 连接到Chrome
    async connect() {
        try {
            const result = await this.request('POST', '/connect');
            console.log('连接结果:', result.message);
            return result.success;
        } catch (error) {
            console.error('连接失败:', error.message);
            return false;
        }
    }

    // 导航到URL
    async navigate(url) {
        try {
            const result = await this.request('POST', '/navigate', { url });
            console.log(`导航到: ${result.url}`);
            console.log(`页面标题: ${result.title}`);
            return result;
        } catch (error) {
            console.error('导航失败:', error.message);
            throw error;
        }
    }

    // 截图
    async screenshot(filename) {
        try {
            const result = await this.request('POST', '/screenshot', { filename });
            console.log(`截图保存为: ${result.filename}`);
            return result;
        } catch (error) {
            console.error('截图失败:', error.message);
            throw error;
        }
    }

    // 点击元素
    async click(selector) {
        try {
            const result = await this.request('POST', '/click', { selector });
            console.log(`点击元素: ${selector}`);
            return result;
        } catch (error) {
            console.error('点击失败:', error.message);
            throw error;
        }
    }

    // 输入文本
    async type(selector, text) {
        try {
            const result = await this.request('POST', '/type', { selector, text });
            console.log(`在 ${selector} 输入: ${text}`);
            return result;
        } catch (error) {
            console.error('输入失败:', error.message);
            throw error;
        }
    }

    // 执行JavaScript
    async evaluate(code) {
        try {
            const result = await this.request('POST', '/evaluate', { code });
            console.log(`执行结果:`, result.result);
            return result;
        } catch (error) {
            console.error('执行失败:', error.message);
            throw error;
        }
    }

    // 获取页面信息
    async getPageInfo() {
        try {
            const result = await this.request('GET', '/page-info');
            console.log('页面信息:');
            console.log(`  标题: ${result.title}`);
            console.log(`  URL: ${result.url}`);
            console.log(`  视口: ${JSON.stringify(result.viewport)}`);
            return result;
        } catch (error) {
            console.error('获取页面信息失败:', error.message);
            throw error;
        }
    }

    // 测试MES系统
    async testMES() {
        try {
            const result = await this.request('POST', '/test-mes');
            console.log('MES测试结果:');
            if (result.success) {
                result.results.forEach(test => {
                    console.log(`  ${test.test}: ${test.status}`);
                });
            } else {
                console.log(`  测试失败: ${result.error}`);
            }
            return result;
        } catch (error) {
            console.error('MES测试失败:', error.message);
            throw error;
        }
    }
}

// 示例用法
async function runExample() {
    console.log('=== MCP客户端示例 ===');
    
    const client = new MCPClient('http://localhost:3001');
    
    try {
        // 1. 检查服务器状态
        console.log('\n1. 检查服务器状态...');
        const isHealthy = await client.checkHealth();
        if (!isHealthy) {
            console.log('服务器不可用，请先启动MCP服务器');
            return;
        }

        // 2. 获取可用工具
        console.log('\n2. 获取可用工具...');
        await client.getTools();

        // 3. 连接到Chrome
        console.log('\n3. 连接到Chrome...');
        const connected = await client.connect();
        if (!connected) {
            console.log('Chrome连接失败');
            return;
        }

        // 4. 导航到MES系统
        console.log('\n4. 导航到MES系统...');
        await client.navigate('http://************:3000');

        // 5. 获取页面信息
        console.log('\n5. 获取页面信息...');
        await client.getPageInfo();

        // 6. 截图
        console.log('\n6. 截图...');
        await client.screenshot('client-example-screenshot.png');

        // 7. 执行JavaScript
        console.log('\n7. 执行JavaScript...');
        await client.evaluate('document.title');

        // 8. 测试MES系统
        console.log('\n8. 测试MES系统...');
        await client.testMES();

        console.log('\n=== 示例完成 ===');
        
    } catch (error) {
        console.error('示例执行失败:', error.message);
    }
}

// 命令行使用
if (require.main === module) {
    const command = process.argv[2];
    const args = process.argv.slice(3);
    
    const client = new MCPClient();
    
    switch (command) {
        case 'health':
            client.checkHealth();
            break;
        case 'tools':
            client.getTools();
            break;
        case 'connect':
            client.connect();
            break;
        case 'navigate':
            if (args[0]) {
                client.navigate(args[0]);
            } else {
                console.log('用法: node mcp-client-example.js navigate <url>');
            }
            break;
        case 'screenshot':
            client.screenshot(args[0]);
            break;
        case 'test':
            client.testMES();
            break;
        case 'example':
        default:
            runExample();
            break;
    }
}

module.exports = MCPClient;
