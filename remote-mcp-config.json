{"mcpServers": {"remote-chrome-browser": {"command": "node", "args": ["-e", "const http = require('http'); const serverUrl = 'http://************:3001'; function request(method, path, data) { return new Promise((resolve, reject) => { const url = new URL(path, serverUrl); const options = { hostname: url.hostname, port: url.port, path: url.pathname, method, headers: { 'Content-Type': 'application/json' } }; const req = http.request(options, (res) => { let responseData = ''; res.on('data', (chunk) => responseData += chunk); res.on('end', () => { try { resolve(JSON.parse(responseData)); } catch (error) { reject(error); } }); }); req.on('error', reject); if (data) req.write(JSON.stringify(data)); req.end(); }); } async function handleMCP() { process.stdin.on('data', async (data) => { const lines = data.toString().trim().split('\\n'); for (const line of lines) { if (line.trim()) { try { const message = JSON.parse(line); let response; switch (message.method) { case 'initialize': response = { jsonrpc: '2.0', id: message.id, result: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, serverInfo: { name: 'remote-chrome-mcp', version: '1.0.0' } } }; break; case 'tools/list': response = { jsonrpc: '2.0', id: message.id, result: { tools: [ { name: 'navigate', description: 'Navigate to a URL', inputSchema: { type: 'object', properties: { url: { type: 'string', description: 'URL to navigate to' } }, required: ['url'] } }, { name: 'screenshot', description: 'Take a screenshot', inputSchema: { type: 'object', properties: { filename: { type: 'string', description: 'Screenshot filename' } } } }, { name: 'click', description: 'Click an element', inputSchema: { type: 'object', properties: { selector: { type: 'string', description: 'CSS selector' } }, required: ['selector'] } }, { name: 'type', description: 'Type text', inputSchema: { type: 'object', properties: { selector: { type: 'string', description: 'CSS selector' }, text: { type: 'string', description: 'Text to type' } }, required: ['selector', 'text'] } }, { name: 'evaluate', description: 'Execute JavaScript', inputSchema: { type: 'object', properties: { code: { type: 'string', description: 'JavaScript code' } }, required: ['code'] } }, { name: 'get_page_info', description: 'Get page information', inputSchema: { type: 'object', properties: {} } }, { name: 'test_mes_system', description: 'Test MES system', inputSchema: { type: 'object', properties: {} } } ] } }; break; case 'tools/call': const { name, arguments: args } = message.params; let apiPath, apiData; switch (name) { case 'navigate': apiPath = '/navigate'; apiData = { url: args.url }; break; case 'screenshot': apiPath = '/screenshot'; apiData = { filename: args.filename }; break; case 'click': apiPath = '/click'; apiData = { selector: args.selector }; break; case 'type': apiPath = '/type'; apiData = { selector: args.selector, text: args.text }; break; case 'evaluate': apiPath = '/evaluate'; apiData = { code: args.code }; break; case 'get_page_info': apiPath = '/page-info'; apiData = null; break; case 'test_mes_system': apiPath = '/test-mes'; apiData = null; break; default: throw new Error('Unknown tool: ' + name); } const result = await request(apiData ? 'POST' : 'GET', apiPath, apiData); response = { jsonrpc: '2.0', id: message.id, result: { content: [{ type: 'text', text: JSON.stringify(result, null, 2) }] } }; break; default: throw new Error('Unknown method: ' + message.method); } console.log(JSON.stringify(response)); } catch (error) { console.log(JSON.stringify({ jsonrpc: '2.0', id: message.id, error: { code: -32603, message: error.message } })); } } } }); process.stdin.resume(); } handleMCP();"], "env": {"REMOTE_MCP_SERVER": "http://************:3001"}}}}